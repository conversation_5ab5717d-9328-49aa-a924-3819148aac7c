# 建筑工地数字孪生模拟沙盘 - 项目业务逻辑规划

## 1. 项目概述

### 1.1 业务目标
本项目旨在构建一个高度仿真的建筑工地数字孪生模拟系统，通过模拟真实工地的各种业务场景，生成高质量的测试数据，为后续的数字孪生系统提供数据基础和功能验证。

### 1.2 核心价值
- **数据驱动**: 生成结构化的工地运营数据
- **场景仿真**: 模拟真实工地的复杂业务场景
- **风险预警**: 模拟安全违规和异常情况
- **效率优化**: 为工地管理提供数据支撑

## 2. 业务架构设计

### 2.1 整体业务架构

```mermaid
graph TB
    subgraph "模拟管理层"
        SM[SimulationManager<br/>模拟管理器]
    end

    subgraph "业务模拟层"
        WS[WorkerSimulator<br/>人员模拟器]
        VS[VehicleSimulator<br/>车辆模拟器]
        ES[EquipmentSimulator<br/>设备模拟器]
        ENS[EnvironmentSimulator<br/>环境模拟器]
        AS[AccessSimulator<br/>门禁模拟器]
        MS[MaterialSimulator<br/>物料模拟器]
    end

    subgraph "数据处理层"
        DC[DataCollector<br/>数据收集器]
        DP[DataProcessor<br/>数据处理器]
        DE[DataExporter<br/>数据导出器]
    end

    SM --> WS
    SM --> VS
    SM --> ES
    SM --> ENS
    SM --> AS
    SM --> MS

    WS --> DC
    VS --> DC
    ES --> DC
    ENS --> DC
    AS --> DC
    MS --> DC

    DC --> DP
    DP --> DE
```

### 2.2 模块间关系

```mermaid
graph LR
    subgraph "环境影响"
        ENV[环境模拟器]
        NOISE[噪声监测]
        DUST[扬尘监测]
    end

    subgraph "人员系统"
        WORKER[人员模拟器]
        SAFETY[安全检查]
        ACCESS[门禁系统]
    end

    subgraph "车辆系统"
        VEHICLE[车辆模拟器]
        TRANSPORT[运输管理]
        MATERIAL[物料管理]
    end

    subgraph "设备系统"
        EQUIPMENT[设备模拟器]
        CRANE[塔吊系统]
        ELEVATOR[升降机系统]
        CAMERA[摄像头系统]
    end

    WORKER --> SAFETY
    WORKER --> ACCESS
    WORKER --> ENV
    SAFETY --> ACCESS

    VEHICLE --> TRANSPORT
    VEHICLE --> ENV
    VEHICLE --> ACCESS
    TRANSPORT --> MATERIAL

    EQUIPMENT --> CRANE
    EQUIPMENT --> ELEVATOR
    EQUIPMENT --> CAMERA
    EQUIPMENT --> ENV

    ENV --> NOISE
    ENV --> DUST
```

## 3. 核心业务模块

### 3.1 人员管理模块

#### 业务职责
- 模拟工人的日常行为和工作状态
- 内置随机安全违规事件生成逻辑
- 记录人员活动轨迹和工作效率数据

#### 核心业务流程

```mermaid
flowchart LR
    A[工人到达] --> B[身份验证]
    B --> C[安全检查]
    C --> D[分配任务]
    D --> E[执行作业]
    E --> F[休息/用餐]
    F --> G[继续作业]
    G --> H[工作结束]
    H --> I[离场登记]

    B --> B1[记录入场时间]
    C --> C1[检查安全装备]
    D --> D1[路径规划]
    E --> E1[进度跟踪]
    E --> E2[随机违规检测]
    F --> F1[时间统计]
    I --> I1[出入记录]

    E2 --> VIOLATION_CHECK{随机违规事件}
    VIOLATION_CHECK -->|触发| VIOLATION_TYPE[确定违规类型]
    VIOLATION_CHECK -->|不触发| NORMAL[正常作业]

    VIOLATION_TYPE --> HELMET_OFF[未佩戴安全帽]
    VIOLATION_TYPE --> NO_VEST[未穿防护服]
    VIOLATION_TYPE --> PHONE_USE[工作时使用手机]
    VIOLATION_TYPE --> DANGER_ZONE[进入危险区域]
    VIOLATION_TYPE --> NO_BELT[未佩戴安全带]
    VIOLATION_TYPE --> EQUIPMENT_MISUSE[违规操作设备]

    HELMET_OFF --> ALERT[生成违规事件]
    NO_VEST --> ALERT
    PHONE_USE --> ALERT
    DANGER_ZONE --> ALERT
    NO_BELT --> ALERT
    EQUIPMENT_MISUSE --> ALERT

    ALERT --> NOTIFY[发送通知]
    ALERT --> LOG[记录日志]

    style ALERT fill:#ff9999
    style A fill:#99ff99
    style I fill:#99ff99
```

#### 随机违规事件配置
- **触发频率**: 每个工人每5-15分钟检查一次
- **违规类型**:
  - 未佩戴安全帽
  - 未穿防护服
  - 工作时使用手机
  - 进入危险区域
  - 未佩戴安全带
  - 违规操作设备
  - 其他违规行为
- **分布概率**: 待实现时根据真实工地数据确定

#### 关键数据输出
- 人员出勤统计
- 随机安全违规事件记录
- 工作状态数据
- 人员轨迹数据
- 违规事件通知消息

### 3.2 车辆管理模块

#### 业务职责
- 管理车辆进出工地的完整流程
- 模拟运输作业和装卸过程
- 内置随机交通违规事件生成逻辑

#### 核心业务流程

```mermaid
flowchart LR
    A[车辆到达] --> B[车牌识别]
    B --> C[身份验证]
    C --> D[称重检测]
    D --> E[路径引导]
    E --> F[装卸作业]
    F --> G[作业确认]
    G --> H[离场称重]
    H --> I[离场确认]

    A --> A1[登记到达时间]
    B --> B1[记录车牌信息]
    C --> C1[验证驾驶员]
    D --> D1[记录载重数据]
    E --> E1[生成导航路径]
    F --> F1[监控作业进度]
    F --> F2[随机违规检测]
    G --> G1[确认货物信息]
    H --> H1[计算净重]
    I --> I1[更新通行记录]

    C --> REJECT[拒绝入场]
    F2 --> VIOLATION_CHECK{随机违规事件}

    VIOLATION_CHECK -->|触发| VIOLATION_TYPE[确定违规类型]
    VIOLATION_CHECK -->|不触发| NORMAL[正常运输]

    VIOLATION_TYPE --> SPEED_VIOLATION[超速行驶]
    VIOLATION_TYPE --> OVERLOAD[超载运输]
    VIOLATION_TYPE --> WRONG_ROUTE[违规路线]
    VIOLATION_TYPE --> WRONG_PARKING[违规停车]

    SPEED_VIOLATION --> ALERT[生成违规事件]
    OVERLOAD --> ALERT
    WRONG_ROUTE --> ALERT
    WRONG_PARKING --> ALERT

    ALERT --> NOTIFY[发送通知]
    ALERT --> LOG[记录日志]

    style REJECT fill:#ff9999
    style ALERT fill:#ff9999
    style A fill:#99ff99
    style I fill:#99ff99
```

#### 随机违规事件配置
- **触发频率**: 每辆车每10-30分钟检查一次
- **违规类型**:
  - 超速行驶
  - 超载运输
  - 违规路线
  - 违规停车
  - 其他违规行为
- **分布概率**: 待实现时根据真实交通数据确定

#### 关键数据输出
- 车辆通行记录
- 货物运输统计
- 装卸效率数据
- 交通违规事件记录
- 违规事件通知消息

### 3.3 设备管理模块

#### 业务职责
- 模拟塔吊、升降机等大型设备运行
- 模拟摄像头监控系统运行
- 内置随机设备故障事件生成逻辑
- 监控设备安全和效率指标

#### 核心业务流程

```mermaid
flowchart LR
    A[设备启动] --> B[安全检查]
    B --> C[作业执行]
    C --> D[状态监控]
    D --> E[维护保养]
    E --> F[设备停机]

    A --> A1[记录启动时间]
    B --> B1[验证安全状态]
    C --> C1[跟踪作业进度]
    C --> C2[摄像头监控]
    D --> D1[随机故障检测]
    E --> E1[计划维护]
    F --> F1[统计运行数据]

    C2 --> CAM_MONITOR[摄像头数据采集]
    CAM_MONITOR --> CAM_ANALYSIS[视频分析]
    CAM_ANALYSIS --> CAM_ALERT[异常检测]

    D1 --> FAULT_CHECK{随机故障事件}
    FAULT_CHECK -->|触发| FAULT_TYPE[确定故障类型]
    FAULT_CHECK -->|不触发| NORMAL[正常运行]

    FAULT_TYPE --> MECHANICAL[机械故障]
    FAULT_TYPE --> ELECTRICAL[电气故障]
    FAULT_TYPE --> OVERLOAD[负载超限]
    FAULT_TYPE --> EMERGENCY[紧急停机]
    FAULT_TYPE --> CAMERA_FAULT[摄像头故障]

    MECHANICAL --> FAULT_EVENT[生成故障事件]
    ELECTRICAL --> FAULT_EVENT
    OVERLOAD --> FAULT_EVENT
    EMERGENCY --> FAULT_EVENT
    CAMERA_FAULT --> FAULT_EVENT
    CAM_ALERT --> FAULT_EVENT

    FAULT_EVENT --> NOTIFY[发送通知]
    FAULT_EVENT --> LOG[记录日志]
    FAULT_EVENT --> MAINTENANCE[安排维护]

    style FAULT_EVENT fill:#ff9999
    style A fill:#99ff99
    style F fill:#99ff99
```

#### 随机故障事件配置
- **触发频率**: 每台设备每30-120分钟检查一次
- **故障类型**:
  - 机械故障
  - 电气故障
  - 负载超限
  - 紧急停机
  - 摄像头故障
- **分布概率**: 待实现时根据真实设备故障统计数据确定

#### 摄像头监控功能
- **监控范围**: 工地关键区域和设备作业区
- **数据采集**: 模拟视频流数据生成
- **异常检测**: 基于规则的异常行为识别
- **数据输出**: 监控画面状态、异常事件记录

#### 关键数据输出
- 设备运行时长
- 作业效率统计
- 随机故障和维护记录
- 设备状态数据
- 摄像头监控数据
- 视频异常事件记录
- 故障事件通知消息

### 3.4 环境监测模块

#### 业务职责
- 监听其他模拟器的活动事件
- 基于事件计算噪声和扬尘数据
- 模拟噪声扬尘在工地环境下的传播和影响

#### 核心业务流程

```mermaid
flowchart LR
    subgraph "事件监听"
        WE[人员活动事件]
        VE[车辆运输事件]
        EE[设备运行事件]
    end

    subgraph "环境计算"
        A[事件接收] --> B[噪声计算]
        A --> C[扬尘计算]
        B --> D[噪声传播模拟]
        C --> E[扬尘扩散模拟]
        D --> F[环境影响评估]
        E --> F
    end

    subgraph "数据输出"
        F --> G[生成环境数据]
        G --> H[阈值检查]
        H --> I[预警判断]
        I --> J[数据记录]
    end

    WE --> A
    VE --> A
    EE --> A

    B --> B1[基础噪声值计算]
    C --> C1[基础扬尘值计算]
    D --> D1[距离衰减算法]
    E --> E1[风向扩散算法]
    F --> F1[叠加效应计算]

    H --> THRESHOLD_CHECK{是否超标}
    THRESHOLD_CHECK -->|超标| ALERT[生成预警]
    THRESHOLD_CHECK -->|正常| NORMAL[正常记录]

    ALERT --> NOTIFY[发送通知]
    ALERT --> LOG[记录日志]
    NORMAL --> LOG

    style A fill:#99ff99
    style J fill:#99ff99
    style ALERT fill:#ff9999
```

#### 环境计算配置
- **噪声计算模型**:
  - 设备运行基础噪声值
  - 车辆运输噪声贡献
  - 距离衰减算法 (反平方定律)
  - 环境噪声叠加计算

- **扬尘计算模型**:
  - 施工活动扬尘系数
  - 车辆行驶扬尘贡献
  - 风速风向影响算法
  - 扬尘沉降和扩散模拟

- **环境阈值**:
  - 噪声限值: 待设定 (如70dB白天/55dB夜间)
  - 扬尘限值: 待设定 (如PM10/PM2.5标准)

#### 关键数据输出
- 实时噪声分布数据
- 实时扬尘浓度数据
- 环境影响评估报告
- 超标预警通知
- 环境质量趋势分析

## 4. 数据流设计

### 4.1 数据生成流程

```mermaid
flowchart LR
    subgraph "数据源"
        SIM[模拟器运行]
        EVENT[业务事件]
        SENSOR[传感器数据]
    end

    subgraph "数据采集"
        COLLECT[数据采集器]
        BUFFER[数据缓冲区]
    end

    subgraph "数据处理"
        PROCESS[数据处理器]
        VALIDATE[数据验证]
        FORMAT[格式转换]
    end

    subgraph "数据存储"
        CACHE[内存缓存]
        DB[数据库]
        FILE[文件存储]
    end

    subgraph "数据导出"
        API[API接口]
        EXPORT[文件导出]
        STREAM[实时流]
    end

    SIM --> COLLECT
    EVENT --> COLLECT
    SENSOR --> COLLECT

    COLLECT --> BUFFER
    BUFFER --> PROCESS

    PROCESS --> VALIDATE
    VALIDATE --> FORMAT

    FORMAT --> CACHE
    FORMAT --> DB
    FORMAT --> FILE

    CACHE --> API
    DB --> EXPORT
    FILE --> EXPORT
    CACHE --> STREAM
```

### 4.2 数据分类体系

#### 实时数据
- 人员位置和状态
- 设备运行参数
- 环境监测数值
- 车辆位置信息

#### 事件数据
- 随机安全违规事件
- 随机设备故障记录
- 环境计算结果数据
- 作业完成事件
- 系统通知消息

#### 统计数据
- 日/周/月报表
- 效率分析报告
- 趋势预测数据
- 对比分析结果

## 5. 系统状态管理

### 5.1 模拟系统状态图

```mermaid
stateDiagram-v2
    [*] --> Initializing : 系统启动

    Initializing --> Ready : 初始化完成
    Ready --> Running : 开始模拟
    Running --> Paused : 暂停模拟
    Paused --> Running : 恢复模拟
    Running --> Stopping : 停止模拟
    Stopping --> Stopped : 清理完成
    Stopped --> Ready : 重新准备

    Running --> Error : 发生错误
    Error --> Recovering : 开始恢复
    Recovering --> Running : 恢复成功
    Recovering --> Stopped : 恢复失败

    state Running {
        [*] --> DataCollecting
        DataCollecting --> EventProcessing
        EventProcessing --> RuleEvaluating
        RuleEvaluating --> DataCollecting

        DataCollecting --> DataExporting : 定时导出
        DataExporting --> DataCollecting : 导出完成
    }
```

### 5.2 异步业务流程

#### 并发业务场景
- 多个工人同时作业
- 多台设备并行运行
- 多辆车辆同时进出
- 多个环境监测点同时采集

#### 异步协调机制
```csharp
// 示例：协调多个模拟器的异步执行
public async UniTask ExecuteSimulationAsync() {
    var tasks = new List<UniTask> {
        workerSimulator.StartSimulationAsync(),
        vehicleSimulator.StartSimulationAsync(),
        equipmentSimulator.StartSimulationAsync(),
        environmentSimulator.StartSimulationAsync()
    };

    await UniTask.WhenAll(tasks);
}
```

#### 事件驱动架构
```mermaid
sequenceDiagram
    participant SM as SimulationManager
    participant WS as WorkerSimulator
    participant VS as VehicleSimulator
    participant ES as EquipmentSimulator
    participant DC as DataCollector

    SM->>WS: StartSimulationAsync()
    SM->>VS: StartSimulationAsync()
    SM->>ES: StartSimulationAsync()

    par 并行执行
        WS->>DC: 发送工人数据
        VS->>DC: 发送车辆数据
        ES->>DC: 发送设备数据
    end

    DC->>SM: 数据收集完成
    SM->>SM: 触发数据导出
```

*本文档将随着项目开发进展持续更新和完善*
